import { BaseEntity } from "../entities/entity";
import * as Errors from "../errors";
import { getStaticDomainPoolService } from "./staticDomainPool";
import {
    ExtendedStaticDomain,
    StaticDomainPoolAttributes
} from "../entities/domainPool";
import { StaticDomainType } from "../entities/domain";

function pickRandom<T>(items: T[]): T {
    const randomIndex = Math.floor(Math.random() * items.length);
    return items[randomIndex];
}

export class EntityStaticDomainPoolService {
    constructor(
        private readonly entity: BaseEntity,
        private readonly domainPoolService = getStaticDomainPoolService()
    ) {
    }

    public async getPool(inherited: boolean = false): Promise<StaticDomainPoolAttributes> {
        if (inherited) {
            const pool = await this.findPool();
            if (!pool) {
                throw new Errors.EntityDomainPoolNotDefinedError();
            }
            return pool;
        } else {
            if (!this.entity.staticDomainPoolId) {
                throw new Errors.EntityDomainPoolNotDefinedError();
            }
            return this.domainPoolService.findById(this.entity.staticDomainPoolId);
        }
    }

    public async addPool(poolId: number): Promise<StaticDomainPoolAttributes> {
        const staticDomainPool = await this.domainPoolService.findById(poolId);
        this.entity.staticDomainPoolId = poolId;
        await this.entity.save();
        return staticDomainPool;
    }

    public async removePool(): Promise<void> {
        this.entity.staticDomainPoolId = null;
        await this.entity.save();
    }

    public async pickStaticDomain(type = StaticDomainType.STATIC): Promise<ExtendedStaticDomain | undefined> {
        const pool = await this.findPool();
        let domains = (pool?.domains || [])
            .filter(({ isActive }) => isActive)
            .filter(domain => domain.type === type);
        if (!domains.length) {
            return undefined;
        }
        return pickRandom(domains);
    }

    private async findPool(): Promise<StaticDomainPoolAttributes | undefined> {
        if (!this.entity.inheritedStaticDomainPoolId) {
            return undefined;
        }
        try {
            const pool = await this.domainPoolService.findById(this.entity.inheritedStaticDomainPoolId);
            return {
                ...pool,
                ...(this.entity.staticDomainPoolId !== this.entity.inheritedStaticDomainPoolId ? { inherited: true } : {})
            };
        } catch (err) {
            if (err instanceof Errors.DomainPoolNotFoundError) {
                return undefined;
            }
            throw err;
        }
    }
}
