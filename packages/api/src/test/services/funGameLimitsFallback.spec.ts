import { expect, should, use } from "chai";
import { stub, SinonStub } from "sinon";
import * as PlayService from "../../skywind/services/playService";
import * as Errors from "../../skywind/errors";
import { PlayMode } from "@skywind-group/sw-wallet-adapter-core";

const chaiAsPromise = require("chai-as-promised");
use(chaiAsPromise);
should();

describe("Fun Game Limits Fallback", () => {
    let findPlayerLimitsStub: SinonStub;
    let getNewLimitsFacadeStub: SinonStub;
    let getFunGameLimitsStub: SinonStub;

    beforeEach(() => {
        // Mock the limits functions to simulate missing limits
        findPlayerLimitsStub = stub(PlayService, "findPlayerLimits" as any);
        getNewLimitsFacadeStub = stub();
        
        // Mock getFunGameLimits to return test limits
        getFunGameLimitsStub = stub().returns({
            stakeAll: [0.01, 0.02, 0.05, 0.1, 0.2, 0.5, 1, 2, 5],
            stakeDef: 0.1,
            stakeMax: 5,
            stakeMin: 0.01,
            winMax: 150000,
            maxTotalStake: 500
        });
    });

    afterEach(() => {
        if (findPlayerLimitsStub.restore) {
            findPlayerLimitsStub.restore();
        }
        if (getNewLimitsFacadeStub.restore) {
            getNewLimitsFacadeStub.restore();
        }
    });

    it("should use fallback limits when LimitsForCurrencyNotFound error occurs in Fun mode", async () => {
        // Simulate the error that occurs when no limits are configured
        findPlayerLimitsStub.rejects(new Errors.LimitsForCurrencyNotFound("TEST_CURRENCY"));

        const mockEntity = {
            id: 1,
            path: "test",
            getCurrencies: () => ["TEST_CURRENCY"]
        };

        const mockEntityGame = {
            game: {
                code: "TEST_GAME",
                type: "slot",
                schemaDefinitionId: 1
            },
            settings: {}
        };

        const mockStartGameTokenData = {
            gameCode: "TEST_GAME",
            brandId: 1,
            playerCode: "TEST_PLAYER",
            currency: "TEST_CURRENCY",
            playmode: PlayMode.FUN
        };

        const mockEntitySettings = {
            newLimitsEnabled: false,
            useGameProviderLimits: false,
            defaultFunGameBalance: 1000
        };

        const mockPlayer = {
            code: "TEST_PLAYER",
            currency: "TEST_CURRENCY"
        };

        // This should not throw an error and should use fallback limits
        try {
            const result = await (PlayService as any).authenticateFunGame(
                mockEntity,
                mockStartGameTokenData,
                mockPlayer,
                mockEntitySettings,
                mockEntityGame
            );
            
            // The result should contain the fallback limits
            expect(result.limits).to.exist;
            expect(result.limits.stakeAll).to.be.an("array");
            expect(result.limits.stakeDef).to.be.a("number");
        } catch (error) {
            // If we get here, the fallback didn't work
            expect.fail("Should have used fallback limits instead of throwing error");
        }
    });

    it("should still throw non-limits-related errors in Fun mode", async () => {
        // Simulate a different kind of error that should not trigger fallback
        const otherError = new Error("Some other error");
        (otherError as any).code = 500;
        
        findPlayerLimitsStub.rejects(otherError);

        const mockEntity = { id: 1, path: "test" };
        const mockEntityGame = { game: { code: "TEST_GAME" }, settings: {} };
        const mockStartGameTokenData = { 
            gameCode: "TEST_GAME", 
            currency: "TEST_CURRENCY",
            playmode: PlayMode.FUN
        };
        const mockEntitySettings = { newLimitsEnabled: false };
        const mockPlayer = {};

        // This should still throw the original error
        try {
            await (PlayService as any).authenticateFunGame(
                mockEntity,
                mockStartGameTokenData,
                mockPlayer,
                mockEntitySettings,
                mockEntityGame
            );
            expect.fail("Should have thrown the original error");
        } catch (error) {
            expect(error.message).to.equal("Some other error");
        }
    });
});
