# Fun Mode Limits Fallback Fix

## Issue Description

When a player opens a game in Fun mode using a currency for which no game limits are configured, the system was throwing a `LimitsForCurrencyNotFound` error (code 104) instead of using the built-in fallback mechanism.

## Root Cause

The issue occurred because:

1. **Configuration Setting**: When `FUN_GAME_START_GAME_TOKEN_REQUIRED=true`, the system would reject ALL errors during Fun game authentication, including missing limits errors.

2. **Missing API Fallback**: The API-based Fun game launch (`packages/api/src/skywind/services/playService.ts`) didn't have the same fallback mechanism as the engine-based launch.

## Solution

### 1. Enhanced Error Handling in Engine (src/skywind/services/auth.ts)

**Before:**
```typescript
} else if (config.funGame.startGameTokenRequired) {
    return Promise.reject(err);  // Rejected ALL errors
}
```

**After:**
```typescript
} else if (config.funGame.startGameTokenRequired && err.code !== 104) {
    // Allow fallback for missing limits (code 104) even when startGameTokenRequired is true
    return Promise.reject(err);
}
```

### 2. Added Fallback Logic to API Service

Added try-catch block in `authenticateFunGame()` function to handle `LimitsForCurrencyNotFound` errors:

```typescript
try {
    // Attempt to get configured limits
    if (entitySettings.newLimitsEnabled && !newLimitsDisabled) {
        limits = await getNewLimitsFacade(entity).buildGameLaunch(entityGame.game, startGameTokenData.currency);
    } else {
        [limits] = await findPlayerLimits(/* ... */);
    }
} catch (err) {
    if (err instanceof Errors.LimitsForCurrencyNotFound) {
        // Use fallback limits for Fun mode
        const { getFunGameLimits } = await import("../../../src/skywind/utils/funGameLimits");
        limits = getFunGameLimits(startGameTokenData.currency);
    } else {
        throw err;
    }
}
```

### 3. Enhanced Logging

Added specific logging for missing limits scenarios to improve monitoring:

```typescript
log.warn({
    currency: startGameTokenData.currency,
    gameCode: startGameTokenData.gameCode,
    brandId: startGameTokenData.brandId,
    error: err.message
}, "No game limits configured for currency in Fun mode - using fallback limits");
```

## How the Fallback Works

The `getFunGameLimits(currency)` function:

1. **Predefined Multipliers**: Uses predefined multipliers for common currencies (stored in `resources/funStartBalanceMultiplier.json`)

2. **Exchange Rate Calculation**: For unknown currencies, calculates multiplier based on USD exchange rate

3. **Prototype Scaling**: Applies the multiplier to a base limits prototype (`resources/funDefaultLimitsPrototype.json`)

4. **Smart Rounding**: Rounds values to preferred remainders [1, 2, 4, 5, 8] × 10^x for user-friendly betting amounts

## Testing

### Manual Test
Run the integration test:
```bash
node test-fun-limits-fallback.js
```

### Unit Tests
```bash
npm test -- --grep "Fun Game Limits Fallback"
```

## Configuration Options

### Option 1: Use Existing Fallback (Recommended)
Keep `FUN_GAME_START_GAME_TOKEN_REQUIRED=true` - the fix now allows fallback for missing limits while still requiring tokens for other errors.

### Option 2: Full Fallback Mode
Set `FUN_GAME_START_GAME_TOKEN_REQUIRED=false` to enable fallback for all Fun mode errors.

## Monitoring

The fix adds specific log entries when fallback limits are used:
- **Log Level**: WARN
- **Message**: "No game limits configured for currency in Fun mode - using fallback limits"
- **Fields**: currency, gameCode, brandId, error

Monitor these logs to identify currencies that need proper limit configuration.

## Benefits

1. **No More Errors**: Fun mode games will load successfully even without configured limits
2. **Automatic Scaling**: Limits are automatically scaled based on currency exchange rates
3. **Better UX**: Players can play immediately without configuration delays
4. **Monitoring**: Clear visibility into when fallback is being used
5. **Backward Compatible**: Existing configurations continue to work unchanged
